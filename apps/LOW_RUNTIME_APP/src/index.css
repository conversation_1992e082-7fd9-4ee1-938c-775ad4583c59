body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

#root {
  height: 100vh;
  overflow: hidden;
}

/* 运行时专用样式 */
.lowcode-runtime {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.lowcode-runtime-toolbar {
  flex-shrink: 0;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
}

.lowcode-runtime-content {
  flex: 1;
  overflow: auto;
}
