import React, { useEffect, useState } from 'react';
import { Button, message, Space, Typography, Alert, Spin } from 'antd';
import { ReloadOutlined, FolderOpenOutlined } from '@ant-design/icons';
import { createRenderer } from '@lowcode/renderer';
import type { PageSchema } from '@lowcode/shared';
import { apiPageSchema } from './apiSchema';

const { Title, Text } = Typography;

// 使用API管理页面Schema作为示例Schema
const exampleSchema: PageSchema = apiPageSchema;


const App: React.FC = () => {
  const [schema, setSchema] = useState<PageSchema | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 加载Schema
  const loadSchema = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // 首先尝试从localStorage加载设计器保存的Schema
      const designerSchema = localStorage.getItem('lowcode-runtime-schema');
      if (designerSchema) {
        const parsed = JSON.parse(designerSchema);
        setSchema(parsed);
        message.success('已加载设计器Schema');
        return;
      }

      // 如果没有设计器Schema，尝试加载保存的Schema
      const savedSchema = localStorage.getItem('lowcode-saved-schema');
      if (savedSchema) {
        const parsed = JSON.parse(savedSchema);
        setSchema(parsed);
        message.success('已加载保存的Schema');
        return;
      }

      // 如果都没有，使用示例Schema
      setSchema(exampleSchema);
      message.info('使用示例Schema');
      
    } catch (err) {
      console.error('Failed to load schema:', err);
      setError('Schema加载失败');
      setSchema(exampleSchema);
    } finally {
      setLoading(false);
    }
  };

  // 从文件加载Schema
  const handleLoadFromFile = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const schema = JSON.parse(e.target?.result as string);
            setSchema(schema);
            localStorage.setItem('lowcode-saved-schema', JSON.stringify(schema));
            message.success('Schema文件加载成功');
          } catch (error) {
            message.error('Schema文件格式错误');
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  // 重新加载
  const handleReload = () => {
    loadSchema();
  };

  // 初始加载
  useEffect(() => {
    loadSchema();
  }, []);

  // 渲染Schema
  const renderSchema = () => {
    if (!schema) return null;

    try {
      const renderer = createRenderer();
      return renderer.renderPage(schema);
    } catch (err) {
      console.error('Render error:', err);
      return (
        <Alert
          message="渲染错误"
          description={`Schema渲染失败: ${err instanceof Error ? err.message : '未知错误'}`}
          type="error"
          showIcon
        />
      );
    }
  };

  if (loading) {
    return (
      <div style={{ 
        height: '100vh', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center' 
      }}>
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* 顶部工具栏 */}
      <div style={{ 
        padding: '12px 24px', 
        borderBottom: '1px solid #f0f0f0',
        background: '#fff',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexShrink: 0
      }}>
        <div>
          <Title level={4} style={{ margin: 0 }}>
            🚀 低代码运行平台
          </Title>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            当前页面: {schema?.title || '未知'}
          </Text>
        </div>
        
        <Space>
          <Button 
            icon={<FolderOpenOutlined />} 
            onClick={handleLoadFromFile}
            size="small"
          >
            加载文件
          </Button>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={handleReload}
            size="small"
          >
            重新加载
          </Button>
        </Space>
      </div>

      {/* 错误提示 */}
      {error && (
        <Alert
          message={error}
          type="error"
          closable
          onClose={() => setError(null)}
          style={{ margin: '16px' }}
        />
      )}

      {/* 主要渲染区域 */}
      <div style={{ flex: 1, overflow: 'auto' }}>
        {renderSchema()}
      </div>
    </div>
  );
};

export default App;
