import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@lowcode/shared': resolve(__dirname, '../../packages/shared/src'),
      '@lowcode/renderer': resolve(__dirname, '../../packages/renderer/src')
    }
  },
  server: {
    port: 3003,
    host: true
  },
  build: {
    outDir: 'dist',
    sourcemap: true
  }
})
