{"name": "lowcode-runtime-app", "version": "1.0.0", "description": "Low-code runtime platform - Schema rendering application", "main": "src/main.tsx", "scripts": {"dev": "vite --port 3003", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint src --ext .ts,.tsx", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@lowcode/shared": "file:../../packages/shared", "@lowcode/renderer": "file:../../packages/renderer", "antd": "^5.0.0", "react-router-dom": "^6.0.0", "axios": "^1.0.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.0.0", "typescript": "^5.0.0", "vite": "^4.0.0", "eslint": "^8.0.0", "rimraf": "^5.0.0"}}