import React from 'react';
import ReactDOM from 'react-dom/client';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import App from './App';
import Preview from './Preview';
import './index.css';
import '../../../packages/shared/src/theme/design.css';

// 根据URL参数决定渲染哪个页面
const urlParams = new URLSearchParams(window.location.search);
const isPreview = urlParams.get('mode') === 'preview';

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <ConfigProvider locale={zhCN}>
      {isPreview ? <Preview /> : <App />}
    </ConfigProvider>
  </React.StrictMode>,
);
