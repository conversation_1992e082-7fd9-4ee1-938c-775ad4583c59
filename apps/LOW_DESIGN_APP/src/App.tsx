import React, { useEffect, useCallback } from 'react';
import { But<PERSON>, message, Space, Typography } from 'antd';
import { SaveOutlined, PlayCircleOutlined } from '@ant-design/icons';
import type { PageSchema } from '@lowcode/shared';
import {
  Designer<PERSON>rov<PERSON>,
  Canvas,
  PropertyDrawer,
  useDesigner,
  DesignComponentSidebar
} from '@lowcode/designer';
import { apiPageSchema } from './apiSchema';

const { Title } = Typography;

// 使用API管理页面Schema作为默认Schema
const defaultSchema: PageSchema = apiPageSchema;


// 设计器主界面组件
const DesignerApp: React.FC = () => {
  const { schema, updateSchema, canvasState, toggleComponentPanel } = useDesigner();

  // 保存Schema到本地存储和文件
  const handleSaveSchema = useCallback(async () => {
    try {
      // 保存到localStorage
      localStorage.setItem('lowcode-designer-schema', JSON.stringify(schema));
      
      // 创建下载链接
      const dataStr = JSON.stringify(schema, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      
      // 创建下载链接
      const link = document.createElement('a');
      link.href = url;
      link.download = `${schema.title || 'page'}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // 清理URL对象
      URL.revokeObjectURL(url);
      
      message.success('Schema已保存并下载');
    } catch (error) {
      console.error('Failed to save schema:', error);
      message.error('保存失败');
    }
  }, [schema]);



  // 预览页面
  const handlePreview = useCallback(() => {
    // 保存当前Schema到localStorage供运行时应用使用
    localStorage.setItem('lowcode-runtime-schema', JSON.stringify(schema));
    
    // 打开运行时应用
    window.open('http://localhost:3004', '_blank');
    message.info('已在新窗口打开预览，请确保运行时应用已启动');
  }, [schema]);

  // 从localStorage加载保存的Schema
  useEffect(() => {
    try {
      const saved = localStorage.getItem('lowcode-designer-schema');
      if (saved) {
        const schema = JSON.parse(saved);
        updateSchema(schema);
      }
    } catch (error) {
      console.warn('Failed to load schema from localStorage:', error);
    }
  }, [updateSchema]);

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* 顶部工具栏 */}
      <div style={{
        padding: '16px 24px',
        borderBottom: '1px solid #f0f0f0',
        background: '#fff',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        {/* 左侧：组件库切换按钮 + 标题 */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <button
            onClick={toggleComponentPanel}
            style={{
              padding: '8px',
              backgroundColor: 'transparent',
              border: '1px solid #d9d9d9',
              borderRadius: '6px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'all 0.2s ease',
              width: '32px',
              height: '32px'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#f5f5f5';
              e.currentTarget.style.borderColor = '#1890ff';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
              e.currentTarget.style.borderColor = '#d9d9d9';
            }}
            title={canvasState.componentPanelCollapsed ? '展开组件库' : '收缩组件库'}
          >
            <span
              className="material-icons-outlined"
              style={{
                fontSize: '18px',
                color: '#666',
                transition: 'transform 0.2s ease'
              }}
            >
              {canvasState.componentPanelCollapsed ? 'menu_open' : 'menu'}
            </span>
          </button>

          <Title level={3} style={{ margin: 0 }}>
            🎨 低代码开发平台
          </Title>
        </div>

        {/* 右侧：操作按钮 */}
        <Space>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={handleSaveSchema}
          >
            保存
          </Button>
          <Button
            type="default"
            icon={<PlayCircleOutlined />}
            onClick={handlePreview}
          >
            预览
          </Button>
        </Space>
      </div>

      {/* 主要设计区域 */}
      <div style={{ flex: 1, display: 'flex', overflow: 'hidden' }}>
        {/* 左侧组件面板 */}
        <DesignComponentSidebar />

        {/* 中间画布区域 */}
        <div style={{ flex: 1, background: '#f5f5f5', position: 'relative', minWidth: 0 }}>
          <Canvas />
        </div>

        {/* 右侧属性面板 */}
        <PropertyDrawer />
      </div>
    </div>
  );
};

// 主应用组件
const App: React.FC = () => {
  return (
    <DesignerProvider initialSchema={defaultSchema}>
      <DesignerApp />
    </DesignerProvider>
  );
};

export default App;
