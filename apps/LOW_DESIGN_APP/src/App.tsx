import React, { useEffect, useCallback } from 'react';
import { But<PERSON>, message, Space, Typography } from 'antd';
import { SaveOutlined, FolderOpenOutlined, PlayCircleOutlined } from '@ant-design/icons';
import type { PageSchema } from '@lowcode/shared';
import {
  DesignerProvider,
  Canvas,
  PropertyDrawer,
  Toolbar,
  useDesigner,
  DesignComponentSidebar
} from '@lowcode/designer';
import { apiPageSchema } from './apiSchema';

const { Title } = Typography;

// 使用API管理页面Schema作为默认Schema
const defaultSchema: PageSchema = apiPageSchema;


// 设计器主界面组件
const DesignerApp: React.FC = () => {
  const { schema, updateSchema } = useDesigner();

  // 保存Schema到本地存储和文件
  const handleSaveSchema = useCallback(async () => {
    try {
      // 保存到localStorage
      localStorage.setItem('lowcode-designer-schema', JSON.stringify(schema));
      
      // 创建下载链接
      const dataStr = JSON.stringify(schema, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      
      // 创建下载链接
      const link = document.createElement('a');
      link.href = url;
      link.download = `${schema.title || 'page'}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // 清理URL对象
      URL.revokeObjectURL(url);
      
      message.success('Schema已保存并下载');
    } catch (error) {
      console.error('Failed to save schema:', error);
      message.error('保存失败');
    }
  }, [schema]);

  // 加载Schema文件
  const handleLoadSchema = useCallback(() => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const schema = JSON.parse(e.target?.result as string);
            updateSchema(schema);
            message.success('Schema加载成功');
          } catch (error) {
            message.error('Schema文件格式错误');
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  }, [updateSchema]);

  // 预览页面
  const handlePreview = useCallback(() => {
    // 保存当前Schema到localStorage供运行时应用使用
    localStorage.setItem('lowcode-runtime-schema', JSON.stringify(schema));
    
    // 打开运行时应用
    window.open('http://localhost:3004', '_blank');
    message.info('已在新窗口打开预览，请确保运行时应用已启动');
  }, [schema]);

  // 从localStorage加载保存的Schema
  useEffect(() => {
    try {
      const saved = localStorage.getItem('lowcode-designer-schema');
      if (saved) {
        const schema = JSON.parse(saved);
        updateSchema(schema);
      }
    } catch (error) {
      console.warn('Failed to load schema from localStorage:', error);
    }
  }, [updateSchema]);

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* 顶部工具栏 */}
      <div style={{ 
        padding: '16px 24px', 
        borderBottom: '1px solid #f0f0f0',
        background: '#fff',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <Title level={3} style={{ margin: 0 }}>
          🎨 低代码开发平台
        </Title>
        
        <Space>
          <Button 
            icon={<FolderOpenOutlined />} 
            onClick={handleLoadSchema}
          >
            加载Schema
          </Button>
          <Button 
            type="primary" 
            icon={<SaveOutlined />} 
            onClick={handleSaveSchema}
          >
            保存Schema
          </Button>
          <Button 
            type="default" 
            icon={<PlayCircleOutlined />} 
            onClick={handlePreview}
          >
            预览运行
          </Button>
        </Space>
      </div>

      {/* 主要设计区域 */}
      <div style={{ flex: 1, display: 'flex', overflow: 'hidden' }}>
        {/* 左侧组件面板 */}
        <div style={{ 
          width: '280px', 
          borderRight: '1px solid #f0f0f0',
          background: '#fafafa',
          overflow: 'auto'
        }}>
          <DesignComponentSidebar />
        </div>

        {/* 中间画布区域 */}
        <div style={{ flex: 1, background: '#f5f5f5', position: 'relative' }}>
          <Canvas />
        </div>

        {/* 右侧属性面板 */}
        <div style={{ width: '320px' }}>
          <PropertyDrawer />
        </div>
      </div>
    </div>
  );
};

// 主应用组件
const App: React.FC = () => {
  return (
    <DesignerProvider initialSchema={defaultSchema}>
      <DesignerApp />
    </DesignerProvider>
  );
};

export default App;
