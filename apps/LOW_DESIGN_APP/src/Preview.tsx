import React, { useEffect, useState } from 'react';
import { createRenderer } from '@lowcode/renderer';
import type { PageSchema } from '@lowcode/shared';

const Preview: React.FC = () => {
  const [schema, setSchema] = useState<PageSchema | null>(null);
  const [renderer] = useState(() => createRenderer());
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    try {
      // 从sessionStorage获取schema数据
      const schemaData = sessionStorage.getItem('lowcode-preview-schema');
      if (schemaData) {
        const parsedSchema = JSON.parse(schemaData);
        setSchema(parsedSchema);
      } else {
        setError('未找到预览数据，请从设计器页面重新打开预览');
      }
    } catch (err) {
      setError('预览数据格式错误');
      console.error('Failed to parse schema:', err);
    }
  }, []);

  if (error) {
    return (
      <div style={{
        height: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'column',
        backgroundColor: '#f5f5f5'
      }}>
        <div style={{
          backgroundColor: '#fff',
          padding: '32px',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
          textAlign: 'center'
        }}>
          <h2 style={{ color: '#ef4444', marginBottom: '16px' }}>预览错误</h2>
          <p style={{ color: '#6b7280', marginBottom: '24px' }}>{error}</p>
          <button
            onClick={() => window.close()}
            style={{
              backgroundColor: '#2563eb',
              color: '#fff',
              border: 'none',
              padding: '8px 16px',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            关闭窗口
          </button>
        </div>
      </div>
    );
  }

  if (!schema) {
    return (
      <div style={{
        height: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f5f5f5'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '24px', marginBottom: '16px' }}>⏳</div>
          <div style={{ color: '#6b7280' }}>加载预览中...</div>
        </div>
      </div>
    );
  }

  return (
    <div style={{ height: '100vh', overflow: 'auto' }}>
      {renderer.renderPage(schema)}
    </div>
  );
};

export default Preview;
