body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

#root {
  height: 100vh;
  overflow: hidden;
}

/* 设计器专用样式 */
.lowcode-designer {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.lowcode-designer-toolbar {
  flex-shrink: 0;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
}

.lowcode-designer-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.lowcode-designer-sidebar {
  flex-shrink: 0;
  border-right: 1px solid #f0f0f0;
  background: #fafafa;
  overflow: auto;
}

.lowcode-designer-canvas {
  flex: 1;
  background: #f5f5f5;
  position: relative;
  overflow: auto;
}

.lowcode-designer-properties {
  flex-shrink: 0;
  border-left: 1px solid #f0f0f0;
  background: #fff;
}
