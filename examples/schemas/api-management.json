{"id": "api_management", "title": "API管理页面", "components": [{"id": "top_nav", "type": "TopNavigation", "props": {"logo": {"src": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiByeD0iNCIgZmlsbD0iIzE4OTBmZiIvPgo8cGF0aCBkPSJNOCAxNmg4djhIOHYtOHoiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik0xNiA4aDh2OGgtOFY4eiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+", "href": "/"}, "menu": [{"key": "overview", "label": "概览", "href": "/overview"}, {"key": "apis", "label": "APIs", "href": "/apis"}, {"key": "data", "label": "数据", "href": "/data"}, {"key": "insight", "label": "洞察", "href": "/insight"}, {"key": "risk", "label": "风险", "href": "/risk"}, {"key": "audit", "label": "审计", "href": "/audit"}, {"key": "config", "label": "配置", "href": "/config"}], "user": {"name": "管理员", "menu": [{"key": "profile", "label": "个人资料"}, {"key": "settings", "label": "设置"}, {"key": "logout", "label": "退出"}]}}}, {"id": "main_layout", "type": "TopDownLayout", "props": {"style": {"minHeight": "calc(100vh - 64px)"}}, "children": [{"id": "sidebar", "type": "SidebarTreeView", "props": {"width": 280, "height": "100%", "searchable": true, "searchPlaceholder": "搜索API/分类", "data": [{"key": "all", "title": "全部API", "count": 212, "href": "/apis/all"}, {"key": "penetration", "title": "渗透测试重点API", "children": [{"key": "login", "title": "登录API", "count": 26}, {"key": "url", "title": "URL重定向API", "count": 5}, {"key": "response", "title": "单次响应数据量过大API", "count": 3}, {"key": "sms", "title": "短信验证码发送API", "count": 2}, {"key": "register", "title": "注册API", "count": 0}]}, {"key": "network", "title": "互联网敏感API"}, {"key": "format", "title": "API格式"}, {"key": "label", "title": "API标签"}]}}, {"id": "content_area", "type": "Container", "props": {"style": {"flex": 1, "padding": "24px", "background": "#fff"}}, "children": [{"id": "page_title", "type": "Text", "props": {"content": "API管理", "style": {"fontSize": "24px", "fontWeight": "bold", "marginBottom": "16px"}}}, {"id": "api_table", "type": "TableViewWithSearch", "props": {"searchPlaceholder": "搜索API名称、路径或描述", "columns": [{"key": "name", "title": "API名称", "width": 200}, {"key": "path", "title": "路径", "width": 300}, {"key": "method", "title": "方法", "width": 100}, {"key": "status", "title": "状态", "width": 100}, {"key": "description", "title": "描述", "width": 300}], "data": [{"key": "1", "name": "用户登录", "path": "/api/auth/login", "method": "POST", "status": "正常", "description": "用户登录接口"}, {"key": "2", "name": "获取用户信息", "path": "/api/user/profile", "method": "GET", "status": "正常", "description": "获取当前用户详细信息"}, {"key": "3", "name": "更新用户信息", "path": "/api/user/profile", "method": "PUT", "status": "正常", "description": "更新用户个人信息"}]}}]}]}, {"id": "status_bar", "type": "StatusBar", "props": {"items": [{"key": "total", "label": "总API数", "value": "212"}, {"key": "active", "label": "活跃API", "value": "198"}, {"key": "error", "label": "异常API", "value": "14"}]}}], "apis": [], "theme": {}, "layout": {"type": "topdown", "header": true, "sidebar": true, "footer": true}}