# 低代码平台

一个基于React的低代码开发平台，采用清晰的架构分离，区分开发平台和运行平台，支持可视化拖拽设计和实时预览。

## 系统架构

### 核心概念

- **低代码开发平台**：可视化设计工具，用于创建和配置页面，产出JSON Schema
- **低代码运行平台**：生产环境渲染引擎，消费JSON Schema并渲染页面
- **共享基础设施**：组件库、主题系统、API管理等，两个平台共享使用

### 架构优势

- **一致性保证**：开发时预览 = 运行时效果
- **职责清晰**：开发工具与运行引擎完全分离
- **共享复用**：组件库统一维护，两端同步受益
- **扩展灵活**：支持独立部署和定制化

## 项目结构

```
lowcode/
├── packages/
│   ├── shared/             # 🏗️ 共享基础设施
│   │   ├── src/
│   │   │   ├── components/ # 业务组件库 (两个平台共享)
│   │   │   │   ├── basic/  # 基础组件 (Button, Input等)
│   │   │   │   ├── business/ # 业务组件 (TopNavigation等)
│   │   │   │   └── layout/ # 布局组件 (TopDownLayout等)
│   │   │   ├── types/      # 共享类型定义 (Schema等)
│   │   │   ├── utils/      # 共享工具函数
│   │   │   ├── api/        # API管理器 (支持模拟和真实请求)
│   │   │   ├── events/     # 事件系统 (组件通信)
│   │   │   ├── themes/     # 主题系统 (样式管理)
│   │   │   └── context/    # React Context
│   │   └── package.json
│   │
│   ├── renderer/           # 🚀 渲染引擎 (运行平台核心)
│   │   ├── src/
│   │   │   └── core/       # Schema解析和渲染逻辑
│   │   └── package.json
│   │
│   └── designer/           # 🎨 设计器 (开发平台核心)
│       ├── src/
│       │   ├── components/ # 设计器UI组件
│       │   ├── context/    # 设计器状态管理
│       │   └── types/      # 设计器类型定义
│       └── package.json
│
├── apps/
│   ├── LOW_DESIGN_APP/     # 🎨 低代码开发平台应用
│   │   ├── src/
│   │   └── package.json
│   │
│   └── LOW_RUNTIME_APP/    # � 低代码运行平台应用
│       ├── src/
│       └── package.json
│
├── examples/
│   ├── simple-app/         # 📱 原示例应用 (将被废弃)
│   └── schemas/            # 📄 设计结果存储 (JSON Schema文件)
│
└── docs/                   # 📚 文档
    └── aidocs/             # 架构和设计文档
```

## 包依赖关系

```
@lowcode/shared (共享基础设施)
├── 业务组件库 (开发预览 + 运行功能)
├── 主题系统 (预览样式 + 生产样式)
├── API管理 (模拟数据 + 真实请求)
└── 事件系统 (设计时 + 运行时)
        ↑                ↑
        │                │
@lowcode/renderer    @lowcode/designer
(运行平台核心)        (开发平台核心)
```

## 核心功能

### 共享基础设施 (@lowcode/shared)
- **业务组件库**：TopNavigation、TableView、TopDownLayout等
- **主题系统**：统一的样式管理和主题切换
- **API管理器**：支持开发时模拟数据和运行时真实请求
- **事件系统**：组件间通信和交互处理
- **类型定义**：Schema、ComponentMeta等共享类型

### 渲染引擎 (@lowcode/renderer)
- **Schema解析**：将JSON Schema转换为React组件树
- **组件渲染**：基于shared包的组件库进行渲染
- **运行时优化**：生产环境性能优化
- **便捷API**：createRenderer等工具函数

### 设计器 (@lowcode/designer)
- **可视化画布**：拖拽设计和实时预览
- **组件面板**：业务组件的可视化选择
- **属性配置**：组件属性的可视化编辑
- **API配置**：数据源的可视化配置
- **Schema导出**：生成标准JSON Schema

## 业务组件库

### 基础组件 (Basic)
- **Button** - 按钮组件
- **Input** - 输入框组件
- **Container** - 容器组件
- **Text** - 文本组件

### 业务组件 (Business)
- **TopNavigation** - 顶部导航栏
- **SidebarTreeView** - 侧边栏树形导航
- **TableViewWithSearch** - 表格搜索视图
- **StatusBar** - 状态栏

### 布局组件 (Layout)
- **TopDownLayout** - 上下布局 (头部+侧边栏+内容+底部)

## 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 构建共享包
```bash
# 构建shared包 (必须先构建)
cd packages/shared && npm run build

# 构建renderer包
cd ../renderer && npm run build

# 构建designer包
cd ../designer && npm run build
```

### 3. 启动独立应用

#### 启动低代码开发平台
```bash
# 安装依赖并启动设计器应用
cd apps/LOW_DESIGN_APP && npm install && npm run dev
```
访问 http://localhost:3001 进行可视化设计。

#### 启动低代码运行平台
```bash
# 安装依赖并启动运行时应用
cd apps/LOW_RUNTIME_APP && npm install && npm run dev
```
访问 http://localhost:3003 查看页面运行效果。

### 4. 单独开发模式
```bash
# 开发shared包
cd packages/shared && npm run dev

# 开发renderer包
cd packages/renderer && npm run dev

# 开发designer包
cd packages/designer && npm run dev
```

## 使用流程

### 1. 设计阶段 (LOW_DESIGN_APP)
1. 启动设计器应用 (http://localhost:3001)
2. 拖拽组件到画布进行可视化设计
3. 配置组件属性、样式和API
4. 实时预览设计效果
5. 保存并导出JSON Schema文件

### 2. 运行阶段 (LOW_RUNTIME_APP)
1. 启动运行时应用 (http://localhost:3003)
2. 加载设计器生成的JSON Schema
3. 渲染完整的业务页面
4. 处理用户交互和数据请求

### 3. 数据流转
```
设计器 → JSON Schema → examples/schemas/ → 运行时应用
```

## 技术栈

### 核心技术
- **React 18** - 前端框架
- **TypeScript** - 类型安全
- **Vite** - 构建工具
- **Ant Design** - UI组件库

### 设计器专用
- **React DnD** - 拖拽功能
- **Monaco Editor** - 代码编辑

### 工具库
- **Axios** - HTTP请求
- **Lodash** - 工具函数

## 架构特点

### ✅ 优势
- **概念清晰**：开发平台 vs 运行平台职责明确
- **一致体验**：开发预览 = 生产效果
- **共享复用**：组件库统一维护
- **独立部署**：支持分离部署和扩展
- **类型安全**：完整的TypeScript支持

### 🎯 适用场景
- 企业内部管理系统快速搭建
- 数据展示页面可视化配置
- 业务流程页面标准化
- 多租户系统界面定制

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

MIT License
