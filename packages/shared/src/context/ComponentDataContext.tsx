import React, { createContext, useContext, ReactNode } from 'react';
import type { ComponentSchema } from '../types';

// 组件数据上下文接口
interface ComponentDataContextType {
  getComponentData: (componentId: string) => ComponentSchema | undefined;
  setComponentData: (componentId: string, data: ComponentSchema) => void;
}

// 创建上下文
const ComponentDataContext = createContext<ComponentDataContextType | undefined>(undefined);

// Provider组件的Props
interface ComponentDataProviderProps {
  children: ReactNode;
  components?: Record<string, ComponentSchema>;
}

// Provider组件
export const ComponentDataProvider: React.FC<ComponentDataProviderProps> = ({
  children,
  components = {}
}) => {
  const [componentData, setComponentData] = React.useState<Record<string, ComponentSchema>>(components);

  // 当components prop更新时，同步更新内部状态
  React.useEffect(() => {
    setComponentData(components);
  }, [components]);

  const getComponentData = (componentId: string): ComponentSchema | undefined => {
    return componentData[componentId];
  };

  const setComponentDataItem = (componentId: string, data: ComponentSchema) => {
    setComponentData(prev => ({
      ...prev,
      [componentId]: data
    }));
  };

  const contextValue: ComponentDataContextType = {
    getComponentData,
    setComponentData: setComponentDataItem
  };

  return (
    <ComponentDataContext.Provider value={contextValue}>
      {children}
    </ComponentDataContext.Provider>
  );
};

// Hook来使用上下文
export const useComponentData = (): ComponentDataContextType => {
  const context = useContext(ComponentDataContext);
  if (!context) {
    throw new Error('useComponentData must be used within a ComponentDataProvider');
  }
  return context;
};

// Hook来获取特定组件的mockData
export const useMockData = (componentId?: string, apiName?: string): any[] => {
  const { getComponentData } = useComponentData();

  return React.useMemo(() => {
    if (!componentId) {
      return [];
    }

    const componentData = getComponentData(componentId);

    if (!componentData || !componentData.mockData) {
      // 返回默认的示例数据，针对TableViewWithSearch组件优化
      return [
        { id: 1, path: '/api/v1/users', sensitivity: '高敏感', riskLevel: '高风险', totalVisits: '1.2千', trafficSource: '192.168.0.1', firstSeen: '2025-08-14 19:18:21' },
        { id: 2, path: '/api/v1/posts', sensitivity: '中敏感', riskLevel: '中风险', totalVisits: '800', trafficSource: '192.168.0.2', firstSeen: '2025-08-14 20:15:33' },
        { id: 3, path: '/api/v1/comments', sensitivity: '低敏感', riskLevel: '低风险', totalVisits: '500', trafficSource: '192.168.0.3', firstSeen: '2025-08-14 21:22:45' }
      ];
    }

    try {
      const mockData = JSON.parse(componentData.mockData);

      // 新格式：支持多个接口
      if (mockData.apis && Array.isArray(mockData.apis)) {
        if (apiName) {
          // 查找指定名称的接口
          const api = mockData.apis.find((api: any) => api.name === apiName);
          return api ? JSON.parse(api.data) : [];
        } else {
          // 返回第一个接口的数据
          const firstApi = mockData.apis[0];
          return firstApi ? JSON.parse(firstApi.data) : [];
        }
      }

      // 兼容旧格式：直接返回数据
      return Array.isArray(mockData) ? mockData : [mockData];
    } catch (error) {
      console.warn('Failed to parse mockData for component:', componentId, error);
      return [];
    }
  }, [componentId, apiName, getComponentData]);
};
