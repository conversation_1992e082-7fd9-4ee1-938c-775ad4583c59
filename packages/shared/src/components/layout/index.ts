import { ComponentMeta } from '../../types';
import { TopDownLayout } from './TopDownLayout';

// 导出组件
export { TopDownLayout };

// TopDownLayout组件元数据
export const topDownLayoutMeta: ComponentMeta = {
  type: 'TopDownLayout',
  name: '上下布局',
  description: '上下布局组件，包含头部、侧边栏、内容区域和底部',
  category: 'layout',
  icon: 'layout',
  props: [
    {
      name: 'header',
      type: 'object',
      description: '头部内容'
    },
    {
      name: 'sidebar',
      type: 'object',
      description: '侧边栏内容'
    },
    {
      name: 'footer',
      type: 'object',
      description: '底部内容'
    },
    {
      name: 'children',
      type: 'object',
      description: '主要内容'
    },
    {
      name: 'sidebarWidth',
      type: 'number',
      description: '侧边栏宽度',
      default: 240
    },
    {
      name: 'headerHeight',
      type: 'number',
      description: '头部高度',
      default: 64
    },
    {
      name: 'footerHeight',
      type: 'number',
      description: '底部高度',
      default: 32
    },
    {
      name: 'sidebarCollapsed',
      type: 'boolean',
      description: '侧边栏是否折叠',
      default: false
    },
    {
      name: 'sidebarCollapsible',
      type: 'boolean',
      description: '侧边栏是否可折叠',
      default: true
    }
  ],
  events: [
    {
      name: 'onSidebarToggle',
      description: '侧边栏折叠切换事件',
      params: [
        { name: 'collapsed', type: 'boolean', description: '是否折叠' }
      ]
    }
  ],
  defaultProps: {
    sidebarWidth: 240,
    headerHeight: 64,
    footerHeight: 32,
    sidebarCollapsed: false,
    sidebarCollapsible: true
  }
};

// 布局组件元数据集合
export const layoutComponentMetas: ComponentMeta[] = [
  topDownLayoutMeta
];

// 布局组件映射
export const layoutComponents = {
  TopDownLayout
};
