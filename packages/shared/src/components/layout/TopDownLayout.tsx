import React from 'react';

export interface TopDownLayoutProps {
  header?: React.ReactNode;
  sidebar?: React.ReactNode;
  footer?: React.ReactNode;
  children?: React.ReactNode;
  sidebarWidth?: number;
  headerHeight?: number;
  footerHeight?: number;
  sidebarCollapsed?: boolean;
  sidebarCollapsible?: boolean;
  style?: React.CSSProperties;
  className?: string;
  onSidebarToggle?: (collapsed: boolean) => void;
}

export const TopDownLayout: React.FC<TopDownLayoutProps> = ({
  header,
  sidebar,
  footer,
  children,
  sidebarWidth = 240,
  headerHeight = 64,
  footerHeight = 32,
  sidebarCollapsed = false,
  sidebarCollapsible = true,
  style,
  className,
  onSidebarToggle,
}) => {
  const [collapsed, setCollapsed] = React.useState(sidebarCollapsed);

  const handleToggle = () => {
    const newCollapsed = !collapsed;
    setCollapsed(newCollapsed);
    onSidebarToggle?.(newCollapsed);
  };

  const currentSidebarWidth = collapsed ? 64 : sidebarWidth;

  const layoutStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    height: '100vh',
    overflow: 'hidden',
    ...style,
  };

  const headerStyle: React.CSSProperties = {
    height: `${headerHeight}px`,
    flexShrink: 0,
    zIndex: 1001,
  };

  const bodyStyle: React.CSSProperties = {
    display: 'flex',
    flex: 1,
    overflow: 'hidden',
  };

  const sidebarStyle: React.CSSProperties = {
    width: `${currentSidebarWidth}px`,
    flexShrink: 0,
    transition: 'width 0.3s ease',
    overflow: 'hidden',
    position: 'relative',
    zIndex: 1000,
  };

  const contentStyle: React.CSSProperties = {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
    minHeight: 0, // 确保flex子元素能正确收缩
  };

  const mainStyle: React.CSSProperties = {
    flex: 1,
    overflow: 'auto',
    padding: '0',
    backgroundColor: '#f5f6f8',
    minHeight: 0, // 确保flex子元素能正确收缩
  };

  const footerStyle: React.CSSProperties = {
    height: `${footerHeight}px`,
    flexShrink: 0,
    zIndex: 999,
    backgroundColor: '#fff',
    borderTop: '1px solid #f0f0f0',
  };

  const toggleButtonStyle: React.CSSProperties = {
    position: 'absolute',
    top: '50%',
    right: '-12px',
    transform: 'translateY(-50%)',
    width: '24px',
    height: '24px',
    backgroundColor: '#ffffff',
    border: '1px solid #d9d9d9',
    borderRadius: '50%',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '12px',
    zIndex: 1002,
    transition: 'all 0.3s',
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
  };

  return (
    <div className={`lowcode-topdown-layout ${className || ''}`} style={layoutStyle}>
      {/* 头部 */}
      {header && (
        <div style={headerStyle}>
          {header}
        </div>
      )}

      {/* 主体区域 */}
      <div style={bodyStyle}>
        {/* 侧边栏 */}
        {sidebar && (
          <div style={sidebarStyle}>
            {sidebar}

            {/* 折叠/展开按钮 */}
            {sidebarCollapsible && (
              <button
                style={toggleButtonStyle}
                onClick={handleToggle}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#f5f5f5';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = '#ffffff';
                }}
              >
                {collapsed ? '▶' : '◀'}
              </button>
            )}
          </div>
        )}

        {/* 内容区域 */}
        <div style={contentStyle}>
          <main style={mainStyle}>
            {children}
          </main>
        </div>
      </div>

      {/* 底部 */}
      {footer && (
        <div style={footerStyle}>
          {footer}
        </div>
      )}
    </div>
  );
};
