import React from 'react';
import { tokens } from '../../theme/tokens';

export interface StatusItem {
  key: string;
  label?: string;
  value: string | number;
  icon?: string;
  color?: string;
  clickable?: boolean;
  onClick?: () => void;
}

export interface StatusBarProps extends React.HTMLAttributes<HTMLDivElement> {
  items: StatusItem[];
  position?: 'fixed' | 'relative';
  height?: number;
  backgroundColor?: string;
  textColor?: string;
  borderTop?: string;
  showSeparator?: boolean;
  style?: React.CSSProperties;
  className?: string;
  onItemClick?: (item: StatusItem) => void;
}

export const StatusBar: React.FC<StatusBarProps> = ({
  items,
  position = 'fixed',
  height = 32,
  backgroundColor = tokens.color.gray50,
  textColor = '#666666',
  borderTop = `1px solid ${tokens.color.border}`,
  showSeparator = true,
  style,
  className,
  onItemClick,
  ...rest
}) => {
  const handleItemClick = (item: StatusItem) => {
    if (item.clickable && item.onClick) {
      item.onClick();
    }
    onItemClick?.(item);
  };

  const defaultStyle: React.CSSProperties = {
    position,
    bottom: position === 'fixed' ? 0 : 'auto',
    left: position === 'fixed' ? 0 : 'auto',
    right: position === 'fixed' ? 0 : 'auto',
    width: '100%',
    height: `${height}px`,
    backgroundColor,
    color: textColor,
    borderTop,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '0 16px',
    fontSize: '12px',
    zIndex: position === 'fixed' ? 1000 : 'auto',
    boxSizing: 'border-box',
    backdropFilter: 'saturate(180%) blur(6px)',
    boxShadow: tokens.shadow.xs,
    ...style,
  };

  const renderStatusItem = (item: StatusItem, index: number) => {
    const itemStyle: React.CSSProperties = {
      display: 'flex',
      alignItems: 'center',
      gap: '4px',
      cursor: item.clickable ? 'pointer' : 'default',
      color: item.color || textColor,
      padding: '4px 8px',
      borderRadius: '4px',
      transition: 'background-color 0.3s',
    };

    return (
      <React.Fragment key={item.key}>
        <div
          style={itemStyle}
          onClick={() => handleItemClick(item)}
          onMouseEnter={(e) => {
            if (item.clickable) {
              e.currentTarget.style.backgroundColor = tokens.color.gray100;
            }
          }}
          onMouseLeave={(e) => {
            if (item.clickable) {
              e.currentTarget.style.backgroundColor = 'transparent';
            }
          }}
        >
          {item.icon && (
            <span style={{ fontSize: '14px' }}>{item.icon}</span>
          )}
          {item.label && (
            <span style={{ marginRight: '4px' }}>{item.label}:</span>
          )}
          <span style={{ fontWeight: '500' }}>{item.value}</span>
        </div>

        {showSeparator && index < items.length - 1 && (
          <div
            style={{
              width: '1px',
              height: '16px',
              backgroundColor: tokens.color.border,
              margin: '0 8px',
            }}
          />
        )}
      </React.Fragment>
    );
  };

  // 将状态项分为左侧和右侧
  const leftItems = items.filter((_, index) => index < Math.ceil(items.length / 2));
  const rightItems = items.filter((_, index) => index >= Math.ceil(items.length / 2));

  return (
    <div className={`lowcode-status-bar ${className || ''}`} style={defaultStyle} {...rest}>
      {/* 左侧状态项 */}
      <div style={{ display: 'flex', alignItems: 'center' }}>
        {leftItems.map((item, index) => renderStatusItem(item, index))}
      </div>

      {/* 右侧状态项 */}
      <div style={{ display: 'flex', alignItems: 'center' }}>
        {rightItems.map((item, index) => renderStatusItem(item, leftItems.length + index))}
      </div>
    </div>
  );
};
