import { ApiConfig } from '../types';

export class ApiManager {
  private apis: Map<string, ApiConfig> = new Map();
  private cache: Map<string, any> = new Map();
  private baseURL: string = '';
  private requestInterceptors: Array<(config: RequestInit) => RequestInit> = [];
  private responseInterceptors: Array<(response: any) => any> = [];

  constructor(baseURL?: string) {
    if (baseURL) {
      this.baseURL = baseURL;
    }
  }

  // 注册API
  registerApi(config: ApiConfig) {
    this.apis.set(config.id, config);
  }

  // 获取API配置
  getApiConfig(id: string): ApiConfig | undefined {
    return this.apis.get(id);
  }

  // 调用API
  async callApi(id: string, params?: Record<string, any>): Promise<any> {
    const config = this.apis.get(id);
    if (!config) {
      throw new Error(`API ${id} not found`);
    }

    const url = this.buildUrl(config.url, params);
    let options = this.buildRequestOptions(config, params);

    // 应用请求拦截器
    for (const interceptor of this.requestInterceptors) {
      options = interceptor(options);
    }

    try {
      const response = await fetch(url, options);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      let data = await response.json();

      // 应用响应拦截器
      for (const interceptor of this.responseInterceptors) {
        data = interceptor(data);
      }

      // 如果配置了数据路径，提取对应数据
      if (config.dataPath) {
        return this.extractDataByPath(data, config.dataPath);
      }

      return data;
    } catch (error) {
      console.error(`API call failed for ${id}:`, error);
      throw error;
    }
  }

  // 带缓存的API调用
  async callApiWithCache(id: string, params?: Record<string, any>, cacheKey?: string): Promise<any> {
    const key = cacheKey || `${id}_${JSON.stringify(params)}`;

    if (this.cache.has(key)) {
      return this.cache.get(key);
    }

    const result = await this.callApi(id, params);
    this.cache.set(key, result);

    return result;
  }

  // 构建URL
  private buildUrl(url: string, params?: Record<string, any>): string {
    let fullUrl = url.startsWith('http') ? url : `${this.baseURL}${url}`;

    if (params) {
      const urlParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          urlParams.append(key, String(value));
        }
      });

      if (urlParams.toString()) {
        fullUrl += `?${urlParams.toString()}`;
      }
    }

    return fullUrl;
  }

  // 构建请求选项
  private buildRequestOptions(config: ApiConfig, params?: Record<string, any>): RequestInit {
    const options: RequestInit = {
      method: config.method,
      headers: {
        'Content-Type': 'application/json',
        ...config.headers,
      },
    };

    // 对于POST/PUT请求，将参数作为请求体
    if (['POST', 'PUT'].includes(config.method) && params) {
      options.body = JSON.stringify(params);
    }

    return options;
  }

  // 根据路径提取数据
  private extractDataByPath(data: any, path: string): any {
    const keys = path.split('.');
    let result = data;

    for (const key of keys) {
      if (result && typeof result === 'object' && key in result) {
        result = result[key];
      } else {
        return undefined;
      }
    }

    return result;
  }

  // 清除缓存
  clearCache(key?: string) {
    if (key) {
      this.cache.delete(key);
    } else {
      this.cache.clear();
    }
  }

  // 设置基础URL
  setBaseURL(url: string) {
    this.baseURL = url;
  }

  // 获取所有API配置
  getAllApis(): ApiConfig[] {
    return Array.from(this.apis.values());
  }

  // 删除API
  removeApi(id: string) {
    this.apis.delete(id);
  }

  // 清空所有API
  clear() {
    this.apis.clear();
    this.cache.clear();
  }

  // 添加请求拦截器
  addRequestInterceptor(interceptor: (config: RequestInit) => RequestInit) {
    this.requestInterceptors.push(interceptor);
  }

  // 添加响应拦截器
  addResponseInterceptor(interceptor: (response: any) => any) {
    this.responseInterceptors.push(interceptor);
  }

  // 移除请求拦截器
  removeRequestInterceptor(interceptor: (config: RequestInit) => RequestInit) {
    const index = this.requestInterceptors.indexOf(interceptor);
    if (index > -1) {
      this.requestInterceptors.splice(index, 1);
    }
  }

  // 移除响应拦截器
  removeResponseInterceptor(interceptor: (response: any) => any) {
    const index = this.responseInterceptors.indexOf(interceptor);
    if (index > -1) {
      this.responseInterceptors.splice(index, 1);
    }
  }

  // 批量调用API
  async callMultipleApis(calls: Array<{ id: string; params?: Record<string, any> }>): Promise<any[]> {
    const promises = calls.map(call => this.callApi(call.id, call.params));
    return Promise.all(promises);
  }

  // 并发调用API（允许部分失败）
  async callMultipleApisAllSettled(calls: Array<{ id: string; params?: Record<string, any> }>): Promise<PromiseSettledResult<any>[]> {
    const promises = calls.map(call => this.callApi(call.id, call.params));
    return Promise.allSettled(promises);
  }

  // 重试API调用
  async retryApi(id: string, params?: Record<string, any>, maxRetries: number = 3, delay: number = 1000): Promise<any> {
    let lastError: Error;

    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await this.callApi(id, params);
      } catch (error) {
        lastError = error as Error;

        if (i < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i))); // 指数退避
        }
      }
    }

    throw lastError!;
  }
}
