import React from 'react';
import {
  ComponentSchema,
  PageSchema,
  RendererConfig,
  ComponentMeta,
  EventAction,
  ComponentRegistry,
  ApiManager,
  EventBus,
  ThemeManager,
  ComponentDataProvider,
  basicComponents,
  basicComponentMetas,
  businessComponents,
  businessComponentMetas,
  layoutComponents,
  layoutComponentMetas
} from '@lowcode/shared';

export class Renderer {
  private componentRegistry: ComponentRegistry;
  private apiManager: ApiManager;
  private eventBus: EventBus;
  private themeManager: ThemeManager;
  // Note: config is used only during construction; no class field needed.

  constructor(config: RendererConfig = {}) {
    // config is applied during construction; no persistent storage required.
    this.componentRegistry = new ComponentRegistry();
    this.apiManager = new ApiManager();
    this.eventBus = new EventBus();
    this.themeManager = new ThemeManager(config.theme);

    // 注册默认组件
    this.registerDefaultComponents();

    // 注册配置中的组件
    if (config.components) {
      Object.values(config.components).forEach(meta => {
        this.componentRegistry.register(meta);
      });
    }

    // 注册API
    if (config.apis) {
      Object.values(config.apis).forEach(api => {
        this.apiManager.registerApi(api);
      });
    }
  }

  // 注册组件
  registerComponent(meta: ComponentMeta, component: React.ComponentType<any>) {
    this.componentRegistry.register(meta, component);
  }

  // 渲染页面
  renderPage(schema: PageSchema): React.ReactElement {
    try {
      // 验证Schema
      this.validatePageSchema(schema);

      // 设置主题
      if (schema.theme) {
        this.themeManager.setTheme(schema.theme);
      }

      // 注册页面API
      if (schema.apis) {
        schema.apis.forEach(api => {
          this.apiManager.registerApi(api);
        });
      }

      // 创建组件数据映射
      const componentDataMap: Record<string, ComponentSchema> = {};
      const collectComponents = (components: ComponentSchema[]) => {
        components.forEach(component => {
          componentDataMap[component.id] = component;
          if (component.children) {
            collectComponents(component.children);
          }
        });
      };
      collectComponents(schema.components);

      // 渲染组件树，包装在ComponentDataProvider中
      return React.createElement(
        ComponentDataProvider,
        {
          components: componentDataMap,
          children: React.createElement(
            'div',
            {
              className: 'lowcode-page',
              'data-page-id': schema.id,
              style: this.themeManager.getPageStyle()
            },
            schema.components.map((component, index) =>
              this.renderComponent(component, `${schema.id}-${index}`)
            )
          )
        }
      );
    } catch (error) {
      console.error('Error rendering page:', error);
      return this.renderErrorBoundary(error as Error, schema);
    }
  }

  // 渲染单个组件
  renderComponent(schema: ComponentSchema, keyPrefix?: string): React.ReactElement {
    try {
      const Component = this.componentRegistry.getComponent(schema.type);

      if (!Component) {
        console.warn(`Component type "${schema.type}" not found`);
        return this.renderUnknownComponent(schema);
      }

      // 处理事件绑定
      const props = this.processProps(schema);

      // 处理子组件
      const children = schema.children?.map((child, index) =>
        this.renderComponent(child, `${keyPrefix || schema.id}-${index}`)
      );

      const key = keyPrefix || schema.id;

      // 为特定组件添加componentId
      const componentProps: any = {
        key,
        ...props,
        'data-component-id': schema.id,
        'data-component-type': schema.type,
        className: this.buildClassName(props.className, schema.className),
        style: this.buildStyle(props.style, schema.style),
      };

      // 为TableViewWithSearch组件添加componentId
      if (schema.type === 'TableViewWithSearch') {
        componentProps.componentId = schema.id;
      }

      return React.createElement(
        Component,
        componentProps,
        children
      );
    } catch (error) {
      console.error(`Error rendering component ${schema.type}:`, error);
      return this.renderComponentError(error as Error, schema);
    }
  }

  // 处理组件属性
  private processProps(schema: ComponentSchema) {
    const props = { ...schema.props };

    // 处理事件
    if (schema.events) {
      Object.entries(schema.events).forEach(([eventName, eventAction]) => {
        props[eventName] = this.createEventHandler(eventAction, schema.id);
      });
    }

    // 处理样式
    if (schema.style) {
      props.style = { ...props.style, ...schema.style };
    }

    // 处理类名
    if (schema.className) {
      props.className = [props.className, schema.className].filter(Boolean).join(' ');
    }

    return props;
  }

  // 创建事件处理器
  private createEventHandler(eventAction: EventAction, componentId: string) {
    return (...args: any[]) => {
      try {
        // 创建执行上下文
        const context = {
          $api: this.apiManager,
          $event: this.eventBus,
          $theme: this.themeManager,
          $componentId: componentId,
          $args: args,
          $config: eventAction.config
        };

        // 根据事件类型执行相应的动作
        switch (eventAction.type) {
          case 'navigate':
            this.handleNavigateAction(eventAction.config, context);
            break;
          case 'showMessage':
            this.handleShowMessageAction(eventAction.config, context);
            break;
          case 'updateState':
            this.handleUpdateStateAction(eventAction.config, context);
            break;
          case 'callApi':
            this.handleCallApiAction(eventAction.config, context);
            break;
          case 'customCode':
            this.handleCustomCodeAction(eventAction.config, context);
            break;
          default:
            console.warn('Unknown event action type:', eventAction.type);
        }
      } catch (error) {
        console.error('Event handler error:', error);
      }
    };
  }

  // 处理导航动作
  private handleNavigateAction(config: any, _context: any) {
    const { url, target = '_self' } = config;
    if (url) {
      if (target === '_blank') {
        window.open(url);
      } else {
        window.location.href = url;
      }
    }
  }

  // 处理显示消息动作
  private handleShowMessageAction(config: any, _context: any) {
    const { message = '操作成功' } = config;
    // 这里可以集成具体的消息组件库
    alert(message);
  }

  // 处理状态更新动作
  private handleUpdateStateAction(config: any, context: any) {
    const { target, data } = config;
    // 触发状态更新事件
    context.$event.emit('updateState', { target, data, componentId: context.$componentId });
  }

  // 处理API调用动作
  private handleCallApiAction(config: any, context: any) {
    const { apiName, params } = config;
    if (apiName && context.$api) {
      context.$api.call(apiName, params).then((result: any) => {
        context.$event.emit('apiResult', { apiName, result, componentId: context.$componentId });
      }).catch((error: any) => {
        console.error('API call error:', error);
      });
    }
  }

  // 处理自定义代码动作
  private handleCustomCodeAction(config: any, context: any) {
    const { code } = config;
    if (code) {
      try {
        const func = new Function('context', `
          const { $api, $event, $theme, $componentId, $args, $config } = context;
          ${code}
        `);
        func(context);
      } catch (error) {
        console.error('Custom code execution error:', error);
      }
    }
  }

  // 注册默认组件
  private registerDefaultComponents() {
    // 注册基础组件
    basicComponentMetas.forEach(meta => {
      const component = basicComponents[meta.type as keyof typeof basicComponents];
      if (component) {
        this.componentRegistry.register(meta, component);
      }
    });

    // 注册业务组件
    businessComponentMetas.forEach(meta => {
      const component = businessComponents[meta.type as keyof typeof businessComponents];
      if (component) {
        this.componentRegistry.register(meta, component);
      }
    });

    // 注册布局组件
    layoutComponentMetas.forEach(meta => {
      const component = layoutComponents[meta.type as keyof typeof layoutComponents];
      if (component) {
        this.componentRegistry.register(meta, component);
      }
    });
  }

  // 获取组件注册表
  getComponentRegistry() {
    return this.componentRegistry;
  }

  // 获取API管理器
  getApiManager() {
    return this.apiManager;
  }

  // 获取事件总线
  getEventBus() {
    return this.eventBus;
  }

  // 获取主题管理器
  getThemeManager() {
    return this.themeManager;
  }

  // 验证页面Schema
  private validatePageSchema(schema: PageSchema): void {
    if (!schema.id) {
      throw new Error('Page schema must have an id');
    }

    if (!schema.components || !Array.isArray(schema.components)) {
      throw new Error('Page schema must have a components array');
    }

    // 验证每个组件
    schema.components.forEach((component, index) => {
      this.validateComponentSchema(component, `components[${index}]`);
    });
  }

  // 验证组件Schema
  private validateComponentSchema(schema: ComponentSchema, path: string = ''): void {
    if (!schema.id) {
      throw new Error(`Component at ${path} must have an id`);
    }

    if (!schema.type) {
      throw new Error(`Component at ${path} must have a type`);
    }

    // 验证子组件
    if (schema.children) {
      schema.children.forEach((child, index) => {
        this.validateComponentSchema(child, `${path}.children[${index}]`);
      });
    }
  }

  // 渲染未知组件
  private renderUnknownComponent(schema: ComponentSchema): React.ReactElement {
    return React.createElement(
      'div',
      {
        key: schema.id,
        'data-component-id': schema.id,
        'data-component-type': schema.type,
        style: {
          padding: '8px',
          border: '2px dashed #ff4d4f',
          borderRadius: '4px',
          color: '#ff4d4f',
          textAlign: 'center' as const,
          fontSize: '12px'
        }
      },
      `Unknown component: ${schema.type}`
    );
  }

  // 渲染组件错误
  private renderComponentError(error: Error, schema: ComponentSchema): React.ReactElement {
    return React.createElement(
      'div',
      {
        key: schema.id,
        'data-component-id': schema.id,
        'data-component-type': schema.type,
        style: {
          padding: '8px',
          border: '2px solid #ff4d4f',
          borderRadius: '4px',
          backgroundColor: '#fff2f0',
          color: '#ff4d4f',
          fontSize: '12px'
        }
      },
      React.createElement('div', { style: { fontWeight: 'bold' } }, 'Component Error'),
      React.createElement('div', null, error.message)
    );
  }

  // 渲染页面错误边界
  private renderErrorBoundary(error: Error, schema: PageSchema): React.ReactElement {
    return React.createElement(
      'div',
      {
        'data-page-id': schema.id,
        style: {
          padding: '20px',
          border: '2px solid #ff4d4f',
          borderRadius: '8px',
          backgroundColor: '#fff2f0',
          color: '#ff4d4f',
          textAlign: 'center' as const
        }
      },
      React.createElement('h2', null, 'Page Render Error'),
      React.createElement('p', null, error.message),
      React.createElement('details', null,
        React.createElement('summary', null, 'Error Details'),
        React.createElement('pre', { style: { textAlign: 'left' as const, fontSize: '12px' } }, error.stack)
      )
    );
  }

  // 构建类名
  private buildClassName(...classNames: (string | undefined)[]): string {
    return classNames.filter(Boolean).join(' ');
  }

  // 构建样式
  private buildStyle(...styles: (React.CSSProperties | undefined)[]): React.CSSProperties {
    return Object.assign({}, ...styles.filter(Boolean));
  }

  // 获取渲染统计信息
  getStats() {
    return {
      registeredComponents: this.componentRegistry.size(),
      registeredApis: this.apiManager.getAllApis().length,
      eventListeners: this.eventBus.eventNames().length,
      currentTheme: this.themeManager.getTheme()
    };
  }

  // 清理资源
  destroy() {
    this.componentRegistry.clear();
    this.apiManager.clear();
    this.eventBus.removeAllListeners();
  }
}
