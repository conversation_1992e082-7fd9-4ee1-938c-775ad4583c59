// 事件配置类型定义
export interface EventConfig {
  id: string;
  name: string;
  description: string;
  icon: string;
  apiConfig?: ApiConfig;
}

export interface ApiConfig {
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  params?: Record<string, any>;
  body?: any;
  timeout?: number;
}

// 组件预定义事件类型
export interface ComponentEventMeta {
  componentType: string;
  events: EventMeta[];
}

export interface EventMeta {
  id: string;
  name: string;
  description: string;
  icon: string;
  trigger: string; // 触发时机描述
  defaultApiConfig?: Partial<ApiConfig>;
}

// 预定义的组件事件配置
export const COMPONENT_EVENTS: ComponentEventMeta[] = [
  {
    componentType: 'TableViewWithSearch',
    events: [
      {
        id: 'onMount',
        name: '初始加载',
        description: '组件挂载时触发，通常用于加载表格数据',
        icon: 'refresh',
        trigger: '组件首次渲染完成时',
        defaultApiConfig: {
          method: 'GET',
          url: '/api/table/data',
          headers: { 'Content-Type': 'application/json' }
        }
      },
      {
        id: 'onSearch',
        name: '搜索事件',
        description: '用户执行搜索操作时触发',
        icon: 'search',
        trigger: '点击搜索按钮或按下回车键时',
        defaultApiConfig: {
          method: 'POST',
          url: '/api/table/search',
          headers: { 'Content-Type': 'application/json' }
        }
      },
      {
        id: 'onRowClick',
        name: '行点击',
        description: '用户点击表格行时触发',
        icon: 'mouse',
        trigger: '点击表格中的任意行时',
        defaultApiConfig: {
          method: 'GET',
          url: '/api/table/row/{id}',
          headers: { 'Content-Type': 'application/json' }
        }
      },
      {
        id: 'onPageChange',
        name: '分页变化',
        description: '用户切换分页时触发',
        icon: 'navigate_next',
        trigger: '点击分页按钮时',
        defaultApiConfig: {
          method: 'GET',
          url: '/api/table/data',
          headers: { 'Content-Type': 'application/json' }
        }
      },
      {
        id: 'onToolbarAction',
        name: '工具栏操作',
        description: '用户点击工具栏按钮时触发',
        icon: 'build',
        trigger: '点击工具栏中的操作按钮时',
        defaultApiConfig: {
          method: 'POST',
          url: '/api/table/action',
          headers: { 'Content-Type': 'application/json' }
        }
      }
    ]
  },
  {
    componentType: 'SidebarTreeView',
    events: [
      {
        id: 'onMount',
        name: '初始加载',
        description: '组件挂载时触发，通常用于加载树形数据',
        icon: 'refresh',
        trigger: '组件首次渲染完成时',
        defaultApiConfig: {
          method: 'GET',
          url: '/api/tree/data',
          headers: { 'Content-Type': 'application/json' }
        }
      },
      {
        id: 'onNodeClick',
        name: '节点点击',
        description: '用户点击树节点时触发',
        icon: 'mouse',
        trigger: '点击树形结构中的节点时',
        defaultApiConfig: {
          method: 'GET',
          url: '/api/tree/node/{key}',
          headers: { 'Content-Type': 'application/json' }
        }
      },
      {
        id: 'onNodeExpand',
        name: '节点展开',
        description: '用户展开树节点时触发',
        icon: 'expand_more',
        trigger: '点击展开/收起图标时',
        defaultApiConfig: {
          method: 'GET',
          url: '/api/tree/children/{key}',
          headers: { 'Content-Type': 'application/json' }
        }
      },
      {
        id: 'onSearch',
        name: '搜索事件',
        description: '用户在搜索框中输入内容时触发',
        icon: 'search',
        trigger: '在搜索框中输入内容时',
        defaultApiConfig: {
          method: 'GET',
          url: '/api/tree/search',
          headers: { 'Content-Type': 'application/json' }
        }
      }
    ]
  },
  {
    componentType: 'TopNavigation',
    events: [
      {
        id: 'onMount',
        name: '初始加载',
        description: '组件挂载时触发，通常用于加载用户信息和菜单权限',
        icon: 'refresh',
        trigger: '组件首次渲染完成时',
        defaultApiConfig: {
          method: 'GET',
          url: '/api/user/info',
          headers: { 'Content-Type': 'application/json' }
        }
      },
      {
        id: 'onMenuClick',
        name: '菜单点击',
        description: '用户点击主菜单项时触发',
        icon: 'menu',
        trigger: '点击顶部导航菜单项时',
        defaultApiConfig: {
          method: 'POST',
          url: '/api/menu/click',
          headers: { 'Content-Type': 'application/json' }
        }
      },
      {
        id: 'onUserMenuClick',
        name: '用户菜单点击',
        description: '用户点击用户下拉菜单项时触发',
        icon: 'account_circle',
        trigger: '点击用户头像下拉菜单项时',
        defaultApiConfig: {
          method: 'POST',
          url: '/api/user/action',
          headers: { 'Content-Type': 'application/json' }
        }
      },
      {
        id: 'onActionClick',
        name: '操作按钮点击',
        description: '用户点击操作按钮时触发',
        icon: 'touch_app',
        trigger: '点击顶部操作按钮时',
        defaultApiConfig: {
          method: 'POST',
          url: '/api/action',
          headers: { 'Content-Type': 'application/json' }
        }
      }
    ]
  }
];

// 获取指定组件类型的预定义事件
export function getComponentEvents(componentType: string): EventMeta[] {
  const componentEventMeta = COMPONENT_EVENTS.find(meta => meta.componentType === componentType);
  return componentEventMeta?.events || [];
}

// 获取所有支持事件的组件类型
export function getSupportedComponentTypes(): string[] {
  return COMPONENT_EVENTS.map(meta => meta.componentType);
}
