import React, { useState } from 'react';
import { useDesigner } from '../../context/DesignerContext';
import { PropertyPanel } from './PropertyPanel';
import { EventPanel } from './EventPanel';
import { MockDataPanel } from './MockDataPanel';

export interface PropertyDrawerProps {
  width?: number;
  maskClosable?: boolean;
  zIndex?: number;
}

/**
 * 右侧属性编辑抽屉：覆盖在预览区上方展示
 * 打开规则：当选中组件时（selectedComponentId 存在）即打开
 */
// Tab配置
const tabs = [
  { key: 'properties', label: '属性编辑', icon: 'tune' },
  { key: 'events', label: '事件配置', icon: 'flash_on' },
  { key: 'mockData', label: '模拟数据', icon: 'data_object' }
];

export const PropertyDrawer: React.FC<PropertyDrawerProps> = ({
  width = 576, // 增加到1.5倍宽度
  maskClosable = true,
  zIndex = 1000
}) => {
  const { canvasState, selectComponent } = useDesigner();
  const open = !!canvasState.selectedComponentId && canvasState.mode !== 'preview';
  const [activeTab, setActiveTab] = useState('properties');

  const drawerStyle: React.CSSProperties = {
    position: 'fixed',
    top: 0,
    right: 0,
    height: '100%',
    width,
    backgroundColor: '#fff',
    boxShadow: '0 0 0 1px rgba(0,0,0,0.06), 0 10px 24px rgba(0,0,0,0.12)',
    transform: `translateX(${open ? 0 : 100}%)`,
    transition: 'transform 0.3s ease-in-out',
    zIndex,
    display: 'flex',
    flexDirection: 'column',
  };

  const maskStyle: React.CSSProperties = {
    position: 'fixed',
    inset: 0 as any,
    backgroundColor: 'rgba(0,0,0,0.3)',
    opacity: open ? 1 : 0,
    pointerEvents: open ? 'auto' : 'none',
    transition: 'opacity 0.3s ease-in-out',
    zIndex: zIndex - 1,
  };

  const handleClose = () => {
    selectComponent(undefined);
  };

  return (
    <>
      {/* 遮罩层 */}
      <div
        style={maskStyle}
        onClick={() => {
          if (maskClosable) handleClose();
        }}
      />

      {/* 抽屉 */}
      <aside style={drawerStyle} onClick={(e) => e.stopPropagation()}>
        {/* 顶部栏 */}
        <div style={{
          height: 56,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '0 16px',
          borderBottom: '1px solid #e8e8e8',
          background: '#fff'
        }}>
          <div style={{ fontSize: 16, fontWeight: 600, color: '#333' }}>组件配置</div>
          <button onClick={handleClose} style={{
            border: 'none',
            background: 'transparent',
            cursor: 'pointer',
            color: '#666',
            fontSize: 14
          }}>关闭 ✕</button>
        </div>

        {/* Tab导航 */}
        <div style={{
          display: 'flex',
          borderBottom: '1px solid #e8e8e8',
          background: '#fff'
        }}>
          {tabs.map(tab => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key)}
              style={{
                flex: 1,
                padding: '12px 16px',
                border: 'none',
                background: activeTab === tab.key ? '#f0f7ff' : 'transparent',
                color: activeTab === tab.key ? '#1890ff' : '#666',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: activeTab === tab.key ? '600' : '400',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '6px',
                borderBottom: activeTab === tab.key ? '2px solid #1890ff' : '2px solid transparent',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                if (activeTab !== tab.key) {
                  e.currentTarget.style.backgroundColor = '#f5f5f5';
                }
              }}
              onMouseLeave={(e) => {
                if (activeTab !== tab.key) {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }
              }}
            >
              <span className="material-icons" style={{ fontSize: '16px' }}>
                {tab.icon}
              </span>
              {tab.label}
            </button>
          ))}
        </div>

        {/* Tab内容 */}
        <div style={{ flex: 1, minHeight: 0, overflow: 'hidden' }}>
          {activeTab === 'properties' && (
            <div style={{ height: '100%', overflow: 'auto' }}>
              <PropertyPanel width={width} />
            </div>
          )}

          {activeTab === 'events' && (
            <div style={{ height: '100%', overflow: 'auto' }}>
              <EventPanel width={width} />
            </div>
          )}

          {activeTab === 'mockData' && (
            <div style={{ height: '100%', overflow: 'auto' }}>
              <MockDataPanel width={width} />
            </div>
          )}
        </div>
      </aside>
    </>
  );
};

