import React, { useState } from 'react';
import { useDesigner } from '../../context/DesignerContext';
import { findComponent } from '@lowcode/renderer';
import { getComponentEvents, EventMeta, ApiConfig } from '../../types/events';

export interface EventPanelProps {
  width?: number;
}

export const EventPanel: React.FC<EventPanelProps> = ({ width: _width = 576 }) => {
  const { schema, canvasState, updateComponent } = useDesigner();
  const [selectedEventId, setSelectedEventId] = useState<string | null>(null);

  // 获取当前选中的组件
  const selectedComponent = canvasState.selectedComponentId
    ? findComponent(schema.components, canvasState.selectedComponentId)
    : null;

  if (!selectedComponent) {
    return (
      <div style={{
        padding: '32px',
        textAlign: 'center',
        color: '#999'
      }}>
        请先选择一个组件
      </div>
    );
  }

  // 获取组件类型的预定义事件
  const predefinedEvents = getComponentEvents(selectedComponent.type);

  // 获取组件的事件配置
  const componentEvents = selectedComponent.events || {};

  // 如果组件类型不支持事件配置
  if (predefinedEvents.length === 0) {
    return (
      <div style={{
        padding: '32px',
        textAlign: 'center',
        color: '#999'
      }}>
        <div style={{ marginBottom: '8px' }}>该组件类型暂不支持事件配置</div>
        <div style={{ fontSize: '12px', color: '#ccc' }}>
          支持的组件：TableViewWithSearch、SidebarTreeView、TopNavigation
        </div>
      </div>
    );
  }

  // 更新事件配置
  const updateEventConfig = (eventId: string, apiConfig: ApiConfig) => {
    const newEvents = {
      ...componentEvents,
      [eventId]: {
        type: 'callApi' as const,
        config: {
          id: eventId,
          apiConfig
        }
      }
    };
    updateComponent(selectedComponent.id, { events: newEvents });
  };

  // 删除事件配置
  const deleteEventConfig = (eventId: string) => {
    const newEvents = { ...componentEvents };
    delete newEvents[eventId];
    updateComponent(selectedComponent.id, { events: newEvents });
    if (selectedEventId === eventId) {
      setSelectedEventId(null);
    }
  };

  // 渲染API配置表单
  const renderApiConfigForm = (eventMeta: EventMeta, currentConfig?: ApiConfig) => {
    const config = currentConfig || {
      url: eventMeta.defaultApiConfig?.url || '',
      method: eventMeta.defaultApiConfig?.method || 'GET',
      headers: eventMeta.defaultApiConfig?.headers || {},
      params: {},
      body: undefined,
      timeout: 5000
    };

    const handleConfigChange = (field: keyof ApiConfig, value: any) => {
      const newConfig = { ...config, [field]: value };
      updateEventConfig(eventMeta.id, newConfig);
    };

    return (
      <div style={{ padding: '16px', backgroundColor: '#f8f9fa', borderRadius: '6px', marginTop: '12px' }}>
        <div style={{ marginBottom: '12px' }}>
          <label style={{ display: 'block', marginBottom: '6px', fontSize: '12px', fontWeight: '500', color: '#333' }}>
            接口地址
          </label>
          <input
            type="text"
            value={config.url}
            onChange={(e) => handleConfigChange('url', e.target.value)}
            placeholder="请输入接口地址，如：/api/data"
            style={{
              width: '100%',
              padding: '8px 10px',
              border: '1px solid #d9d9d9',
              borderRadius: '4px',
              fontSize: '12px',
              outline: 'none'
            }}
          />
        </div>

        <div style={{ marginBottom: '12px' }}>
          <label style={{ display: 'block', marginBottom: '6px', fontSize: '12px', fontWeight: '500', color: '#333' }}>
            请求方法
          </label>
          <select
            value={config.method}
            onChange={(e) => handleConfigChange('method', e.target.value)}
            style={{
              width: '100%',
              padding: '8px 10px',
              border: '1px solid #d9d9d9',
              borderRadius: '4px',
              fontSize: '12px',
              outline: 'none'
            }}
          >
            <option value="GET">GET</option>
            <option value="POST">POST</option>
            <option value="PUT">PUT</option>
            <option value="DELETE">DELETE</option>
          </select>
        </div>

        <div style={{ marginBottom: '12px' }}>
          <label style={{ display: 'block', marginBottom: '6px', fontSize: '12px', fontWeight: '500', color: '#333' }}>
            请求头 (JSON格式)
          </label>
          <textarea
            value={JSON.stringify(config.headers || {}, null, 2)}
            onChange={(e) => {
              try {
                const headers = JSON.parse(e.target.value);
                handleConfigChange('headers', headers);
              } catch (error) {
                // 忽略JSON解析错误，用户输入过程中可能不完整
              }
            }}
            placeholder='{"Content-Type": "application/json"}'
            style={{
              width: '100%',
              height: '60px',
              padding: '8px 10px',
              border: '1px solid #d9d9d9',
              borderRadius: '4px',
              fontSize: '11px',
              fontFamily: 'Monaco, Consolas, "Courier New", monospace',
              resize: 'none',
              outline: 'none'
            }}
          />
        </div>

        <div style={{ marginBottom: '12px' }}>
          <label style={{ display: 'block', marginBottom: '6px', fontSize: '12px', fontWeight: '500', color: '#333' }}>
            超时时间 (毫秒)
          </label>
          <input
            type="number"
            value={config.timeout || 5000}
            onChange={(e) => handleConfigChange('timeout', parseInt(e.target.value))}
            min="1000"
            max="30000"
            style={{
              width: '100%',
              padding: '8px 10px',
              border: '1px solid #d9d9d9',
              borderRadius: '4px',
              fontSize: '12px',
              outline: 'none'
            }}
          />
        </div>

        <div style={{ display: 'flex', gap: '8px', marginTop: '16px' }}>
          <button
            onClick={() => deleteEventConfig(eventMeta.id)}
            style={{
              padding: '6px 12px',
              backgroundColor: '#ff4d4f',
              color: '#ffffff',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '12px'
            }}
          >
            删除配置
          </button>
        </div>
      </div>
    );
  };

  return (
    <div style={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      backgroundColor: '#ffffff'
    }}>
      {/* 标题栏 */}
      <div style={{
        padding: '16px 20px',
        borderBottom: '1px solid #e8e8e8',
        backgroundColor: '#ffffff'
      }}>
        <div style={{ fontSize: 14, fontWeight: 600, color: '#262626', marginBottom: 4 }}>
          事件配置
        </div>
        <div style={{ fontSize: 12, color: '#8c8c8c' }}>
          {selectedComponent.type} (ID: {selectedComponent.id})
        </div>
      </div>

      {/* 事件列表 */}
      <div style={{ flex: 1, overflow: 'auto', padding: '16px 20px' }}>
        {predefinedEvents.map((eventMeta) => {
          const isConfigured = componentEvents[eventMeta.id];
          const isSelected = selectedEventId === eventMeta.id;

          return (
            <div
              key={eventMeta.id}
              style={{
                marginBottom: '12px',
                border: '1px solid #e8e8e8',
                borderRadius: '6px',
                backgroundColor: isConfigured ? '#f6ffed' : '#ffffff'
              }}
            >
              <div
                onClick={() => setSelectedEventId(isSelected ? null : eventMeta.id)}
                style={{
                  padding: '12px 16px',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <span className="material-icons" style={{ fontSize: '18px', color: '#1890ff' }}>
                    {eventMeta.icon}
                  </span>
                  <div>
                    <div style={{ fontSize: '14px', fontWeight: '500', color: '#262626' }}>
                      {eventMeta.name}
                    </div>
                    <div style={{ fontSize: '12px', color: '#8c8c8c', marginTop: '2px' }}>
                      {eventMeta.description}
                    </div>
                    <div style={{ fontSize: '11px', color: '#999', marginTop: '2px' }}>
                      触发时机：{eventMeta.trigger}
                    </div>
                  </div>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  {isConfigured && (
                    <span style={{
                      fontSize: '11px',
                      color: '#52c41a',
                      backgroundColor: '#f6ffed',
                      padding: '2px 6px',
                      borderRadius: '4px',
                      border: '1px solid #b7eb8f'
                    }}>
                      已配置
                    </span>
                  )}
                  <span className="material-icons" style={{ fontSize: '16px', color: '#999' }}>
                    {isSelected ? 'expand_less' : 'expand_more'}
                  </span>
                </div>
              </div>

              {isSelected && (
                <div style={{ borderTop: '1px solid #e8e8e8' }}>
                  {renderApiConfigForm(eventMeta, isConfigured?.config?.apiConfig)}
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};